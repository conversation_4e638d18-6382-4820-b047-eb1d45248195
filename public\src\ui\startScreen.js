// startScreen.js
import { renderDOM } from '../../msec/framework/dom.js';
import { setState } from '../../msec/framework/state.js';
import { navigate } from '../../msec/framework/router.js';
import { send, startNewSession, hasStoredSession } from '../multiplayer/socket.js';

export function StartScreen() {
  const root = document.getElementById('app');

  // Check if user has an existing session
  const hasSession = hasStoredSession();
  const vdom = {
    tag: 'div',
    attrs: { class: 'splash-container' },
    children: [
      {
        tag: 'div',
        attrs: { class: 'splash-screen' },
        children: [
          {
            tag: 'div',
            attrs: { class: 'logo-container' },
            children: [
              {
                tag: 'img',
                attrs: {
                  src: '../static/images/logo.png',
                  alt: 'Bomberman Logo',
                  class: 'logo',
                },
                children: [],
              },
            ],
          },
          {
            tag: 'div',
            attrs: { class: 'menu' },
            children: [
              {
                tag: 'a',
                attrs: { href: '/player', id: 'start-game-btn' },
                children: [hasSession ? 'Continue Game' : 'Start Game'],
              },
              ...(hasSession ? [{
                tag: 'a',
                attrs: { href: '#', id: 'new-game-btn' },
                children: ['New Game'],
              }] : []),
              {
                tag: 'a',
                attrs: { href: '#' },
                children: ['exit'],
              },
            ],
          },
        ],
      },
    ],
  };

  renderDOM(vdom, root);

  // Add event handlers
  const newGameBtn = document.getElementById('new-game-btn');
  if (newGameBtn) {
    newGameBtn.addEventListener('click', (e) => {
      e.preventDefault();
      startNewSession();
      navigate('/player');
    });
  }
}

